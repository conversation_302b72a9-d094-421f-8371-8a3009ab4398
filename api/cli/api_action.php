<?php

use lib\Cfg\Cfg;

/**
 * Консольный инструмент для управления внешними api
 *
 * Основные вызовы:
 *
 * Загрузка городов вендора (требуется вызывать периодически)
 * php api_action.php --action=reload_city --partner_id=##### --http_host=#####
 *
 * Загрузка ride_segment вендора для существующих городов
 * php api_action.php --action=download_rides --partner_id==##### --http_host==##### --forks=#####
 *
 * Обновление ride_segment вендора для существующих ride_segment
 * php api_action.php --action=update_rides --partner_id==##### --http_host==##### --forks=#####
 *
 *
 * @var $app core\app
 */

/** @var int $cronControlTtlSecondOverride Время блокировки в секундах */
//$cronControlTtlSecondOverride = 60*60;

// @todo Прокомментить методы
$_SERVER['DOCUMENT_ROOT'] = __DIR__ . '/..';
require_once($_SERVER['DOCUMENT_ROOT'] . '/lbaf/initconsole.php');

if (isset($app->args->partner)) {
    $vendor_config = $app->partner_model->get_vendor_api_config_with_title($app->args->partner);
    if ($vendor_config) {
        $app->args->partner_id = $vendor_config->vendor_id;
    } else {
        $app->log('Не указан параметр partner', 'FAILURE');
        die();
    }
}

if (!isset($app->args->action)) {
    $app->log('Не указан параметр action', 'FAILURE');
    die();
}

if (isset($app->args->forks)) {
    $app->args->forks = (int)$app->args->forks;
    if (($app->args->forks < 1) || ($app->args->forks > 200)) {
        $app->log('Не корректное значение forks', 'FAILURE');
        die();
    }
} else {
    $app->args->forks = 1;
    $app->log('forks set to ' . $app->args->forks, 'NOTE');
}

if (isset($app->args->city_start)) {
    $city_start = str_replace(' ', '', $app->args->city_start);
    $city_start = explode(',', $city_start);
    if (!$city_start) {
        $app->log('Неверно задан параметр city_start. Пример --city_start=1,2,3,5', 'FAILURE');
        die();
    }
    foreach ($city_start as $item) {
        if (!(int)$item) {
            $app->log('Неверно задан параметр city_start. Пример --city_start=1,2,3,5', 'FAILURE');
            die();
        }
    }
} else {
    $city_start = [];
}

if (isset($app->args->city_end) || isset($app->args->city_end_date)) {
    if (isset($app->args->city_end)) {
        $city_end = str_replace(' ', '', $app->args->city_end);
    }
    //lifehack city_end = "check" string command
    if (isset($app->args->city_end_date) || (isset($city_end) && ($city_end === 'new' || $city_end === 'check'))) { //проверка городов в которые нет рейсов
        $sql_where = [];
        $sql_where[] = 'xref_city_partner.partner_id = ' . (int)$app->args->partner_id . ' ';
        if (isset($app->args->city_end_date)) {
            $sql_where[] = ' xref_city_partner.datetime_merge >= "' . sql_escape($app->args->city_end_date) . ' 00:00:00" ';
        }
        if (isset($city_end) && ($city_end === 'new' || $city_end === 'check')) {
            $sql_where[] = ' xref_city_partner.outer_id not in (select distinct outer_id_to from ride_segment where ride_segment.partner_id = ' . (int)$app->args->partner_id . ' ) ';
        }

        $city_end = $app->db->query('
			select
				xref_city_partner.city_id
			from
				xref_city_partner
			where
				' . join(' and ', $sql_where) . '
			'
        )->result_field_array('city_id', 'integer');
    } else {
        $city_end = explode(',', $city_end);
        if (!$city_end) {
            $app->log('Неверно задан параметр city_end. Пример --city_end=1,2,3,5', 'FAILURE');
            die();
        }
        foreach ($city_end as $item) {
            if (!(int)$item) {
                $app->log('Неверно задан параметр city_end. Пример --city_end=1,2,3,5', 'FAILURE');
                die();
            }
        }
    }
} else {
    $city_end = [];
}

if (isset($app->args->memory_limit)) {
    $memoryLimit = (int)$app->args->memory_limit;
    $memoryLimit = min($memoryLimit, 8192);
} else {
    $memoryLimit = null;
}

if (isset($app->args->api_timeout)) {
    Cfg::$apiTimeout = $app->args->api_timeout;
    $app->log("OK: set api_timeout=" . $app->args->api_timeout);
}

$opt = [];
// Используем новую централизованную систему конфигурации
$config = \lib\Cfg\Cfg::getConfig('api');
$config->database->debug = false;
$config->outer_api_log = isset($app->args->outer_api_log) ? (bool)$app->args->outer_api_log : false;
switch ($app->args->action) {
    case 'test':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }

        $app->db->transaction_start();
        $app->log('starting trans');
        $app->db->query('select * from operation where operation_id=2 for update ');
        $app->log('started!');
        foreach ([1, 2, 3, 4, 5, 6, 7, 8, 9] as $i) {
            sleep(1);
            $app->log($i);
        }
        $app->db->transaction_rollback();
        $app->log('rollback!');
        break;

    case 'reload_city':
    case 'city':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $app->log('Начало обновления городов (для партнёра с ИД = ' . $app->args->partner_id . ').');
        $opt += (array)getopt(null, ['autoLink::']);
        $app->connector_manager->reload_city($app->args->partner_id, $opt);
        $app->log('Завершено обновление городов.');
        break;

    case 'download_rides':
    case 'rides':
    case 'ride':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $opt += (array)getopt(null, ['log_print::']);
        $app->log('Начало загрузки рейсов (для партнёра с ИД = ' . $app->args->partner_id . ').');
        $date_start_shift = (isset($app->args->date_start_shift)) ? $app->args->date_start_shift : 0;
        $date_end_shift = (isset($vendor_config->date_end_shift)) ? $vendor_config->date_end_shift : 21;
        $date_end_shift = (isset($app->args->date_end_shift)) ? $app->args->date_end_shift : $date_end_shift;
        if (!isset($app->args->date_start)) {
            $app->args->date_start = date('Y-m-d', strtotime('now + ' . $date_start_shift . ' days'));
            $app->log('date_start set to ' . $app->args->date_start, 'NOTE');
        }
        if (!isset($app->args->date_end)) {
            $app->args->date_end = date('Y-m-d', strtotime('now + ' . $date_end_shift . ' days'));
            $app->log('date_end set to ' . $app->args->date_end, 'NOTE');
        }
        if (isset($app->args->clever)) {
            $mode = 'clever';
        } else {
            $mode = 'full';
        };
        if (isset($app->args->waypoints)) {
            $waypoints = true;
        } else {
            $waypoints = false;
        };
        $app->connector_manager->download_rides(
            $app->args->partner_id,
            $app->args->date_start,
            $app->args->date_end,
            $city_start,
            $city_end,
            $mode,
            $waypoints,
            $opt
        );
        break;
    case 'ride_all':
        $memoryLimitValue = !is_null($memoryLimit) ? $memoryLimit . 'M' : '2048M';
        if (ini_set('memory_limit', $memoryLimitValue) === false) {
            $app->log("Error: unable to increase the allowed memory to " . $memoryLimitValue, 'FAILURE');
            die();
        } else {
            $app->log("OK: increase allowed memory to " . $memoryLimitValue);
        }

        $opt += (array)getopt(null, ['log_print::']);

        $date_start_shift = (isset($app->args->date_start_shift)) ? $app->args->date_start_shift : 0;
        $date_end_shift = (isset($vendor_config->date_end_shift)) ? $vendor_config->date_end_shift : 21;
        $date_end_shift = (isset($app->args->date_end_shift)) ? $app->args->date_end_shift : $date_end_shift;
        if (!isset($app->args->date_start)) {
            $app->args->date_start = date('Y-m-d', strtotime('now + ' . $date_start_shift . ' days'));
            $app->log('date_start set to ' . $app->args->date_start, 'NOTE');
        }
        if (!isset($app->args->date_end)) {
            $app->args->date_end = date('Y-m-d', strtotime('now + ' . $date_end_shift . ' days'));
            $app->log('date_end set to ' . $app->args->date_end, 'NOTE');
        }
        if (isset($app->args->waypoints)) {
            $waypoints = true;
        } else {
            $waypoints = false;
        };
        $waypoints = false;//во сех кронах отдельно грузится. лишний параметр


        if (empty($app->args->partner_id)) {
            //перебрать всех
            $partner_list = $app->partner_model->get_list_by_params([
                "is_deleted" => 0,
                "type_vendor" => 3,
            ]);

            foreach ($partner_list as $idx => $partner) {
                if (!empty($partner->config_data)) {
                    $config = json_decode($partner->config_data);
                    if ($config->is_nsi_adapter == 'true') {
                        unset($partner_list[$idx]);
                    }
                }
            }

            foreach ($partner_list as $partner) {
                $app->log('Начало загрузки рейсов (для партнёра с ИД = ' . $partner->partner_id . ').');
                try {
                    $app->connector_manager->download_rides_from_all_cities(
                        $partner->partner_id,
                        $app->args->date_start,
                        $app->args->date_end,
                        $waypoints,
                        $opt
                    );
                } catch (Exception $e) {
                    $app->log('Ошибка при получении рейсов. ' . $e->getMessage(), 'FAILURE');
                }
            }
        } else {
            $app->log('Начало загрузки рейсов (для партнёра с ИД = ' . $app->args->partner_id . ').');
            try {
                $app->connector_manager->download_rides_from_all_cities(
                    $app->args->partner_id,
                    $app->args->date_start,
                    $app->args->date_end,
                    $waypoints,
                    $opt
                );
            } catch (Exception $e) {
                $app->log('Ошибка при получении рейсов. ' . $e->getMessage(), 'FAILURE');
            }
        }

        break;

    case 'waypoints':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        if (!isset($app->args->date_start)) {
            $app->args->date_start = date('Y-m-d', strtotime('now'));
            $app->log('date_start set to ' . $app->args->date_start, 'NOTE');
        }
        if (!isset($app->args->date_end)) {
            $app->args->date_end = false;
            $app->log('date_end set to future', 'NOTE');
        }
        $app->log('Начало загрузки промежуточных точек.');
        //$app->log('Partner_id ='.$app->args->partner_id.' city_start='.implode(',',$city_start).' city_end='.implode(',',$city_end) );
        $app->connector_manager->download_waypoints(
            $app->args->partner_id,
            $app->args->date_start,
            $app->args->date_end,
            $city_start,
            $city_end
        );
        break;

    case 'waypoints_all':
        $memoryLimitValue = !is_null($memoryLimit) ? $memoryLimit . 'M' : '2048M';
        if (ini_set('memory_limit', $memoryLimitValue) === false) {
            $app->log("Error: unable to increase the allowed memory to " . $memoryLimitValue, 'FAILURE');
            die();
        } else {
            $app->log("OK: increase allowed memory to " . $memoryLimitValue);
        }

        if (!isset($app->args->date_start)) {
            $app->args->date_start = date('Y-m-d', strtotime('now'));
            $app->log('date_start set to ' . $app->args->date_start, 'NOTE');
        }
        if (!isset($app->args->date_end)) {
            $app->args->date_end = false;
            $app->log('date_end set to future', 'NOTE');
        }
        $app->log('Начало загрузки промежуточных точек.');


        if (empty($app->args->partner_id)) {
            //перебрать всех
            $partner_list = $app->partner_model->get_list_by_params([
                "is_deleted" => 0,
                "type_vendor" => 3,
            ]);

            foreach ($partner_list as $idx => $partner) {
                if (!empty($partner->config_data)) {
                    $config = json_decode($partner->config_data);
                    if ($config->is_nsi_adapter == 'true') {
                        unset($partner_list[$idx]);
                    }
                }
            }

            foreach ($partner_list as $partner) {
                try {
                    $app->connector_manager->download_waypoints_all_segments(
                        $partner->partner_id,
                        $app->args->date_start,
                        $app->args->date_end
                    );
                } catch (Exception $e) {
                    $app->log('Ошибка при получении waypoint-ов. ' . $e->getMessage(), 'FAILURE');
                }
            }
        } else {
            try {
                $app->connector_manager->download_waypoints_all_segments(
                    $app->args->partner_id,
                    $app->args->date_start,
                    $app->args->date_end
                );
            } catch (Exception $e) {
                $app->log('Ошибка при получении waypoint-ов. ' . $e->getMessage(), 'FAILURE');
            }
        }

        break;

    case 'make_sitemap':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $dayStart = date('Y-m-d', strtotime('+1 day'));
        $nextRequest = date('Y-m-d', strtotime('+7 day'));
        $nextRequestError = date('Y-m-d', strtotime('+30 day'));

        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_SCHEME'] = 'http';

        $msm = new lib\Avn\MakeSiteMap();
        $result = $msm->Run($dayStart, $nextRequest, $nextRequestError);

        if (count($result) > 1) {
            $app->log('Перебор всех возможных направлений:');
            $app->log('');
            foreach ($result as $key => $str) {
                $app->log('        ' . $key . ' => ' . $str);
            }
            $app->log('');
        } elseif (count($result) == 1) {
            foreach ($result as $key => $str) {
                $app->log($key . ' => ' . $str);
            }
        }
        //else $app->log('Sync ok.');

        break;

    case 'calc_route_weight':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_SCHEME'] = 'http';

        $msm = new lib\Avn\MakeSiteMap();
        $result = $msm->RunCalcWeight();

        if (count($result) > 1) {
            $app->log('');
            foreach ($result as $key => $str) {
                $app->log('        ' . $key . ' => ' . $str);
            }
            $app->log('');
        } elseif (count($result) == 1) {
            foreach ($result as $key => $str) {
                $app->log($key . ' => ' . $str);
            }
        }
        break;


    case 'load_terminal':
    case 'load_all_terminal_data':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        try {
            $app->log("");
            $memoryLimitValue = !is_null($memoryLimit) ? $memoryLimit . 'M' : '2048M';
            if (ini_set('memory_limit', $memoryLimitValue) === false) {
                $app->log("Error: unable to increase the allowed memory to " . $memoryLimitValue, 'FAILURE');
                die();
            } else {
                $app->log("OK: increase allowed memory to " . $memoryLimitValue);
            }

            $allTdFlag = true;
            $NSI = new \lib\Avn\TerminalNSILoadLib();
            $httpTimeout = \lib\Cfg\Cfg::$api['nsi_http_timeout'];
            $NSI->setHttpTimeout($httpTimeout);

            $TLIST = $NSI->LoadTerminalList(); // список терминалов
            $ULIST = $NSI->LoadUserList(); // список пользователей
            $OLIST = $NSI->LoadOrganizationList(); // организации
            $RFLIST = $NSI->LoadUserRoleFunction(); // функции ролей
            $RLIST = $NSI->LoadUserRidesFast(); // сегменты рейса
        } catch (Exception $e) {
            $app->log("Error during process load from NSI." . $e->getMessage(), 'FAILURE');
            die();
        }

        break;


    case 'load_user_rides':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        try {
            $app->log("");
            $memoryLimitValue = !is_null($memoryLimit) ? $memoryLimit . 'M' : '2048M';
            if (ini_set('memory_limit', $memoryLimitValue) === false) {
                $app->log("Error: unable to increase the allowed memory to " . $memoryLimitValue, 'FAILURE');
                die();
            } else {
                $app->log("OK: increase allowed memory to " . $memoryLimitValue);
            }

            $allTdFlag = true;
            $_SERVER['REQUEST_METHOD'] = 'GET';
            $_SERVER['REQUEST_SCHEME'] = 'http';
            $NSI = new \lib\Avn\TerminalNSILoadLib();
            // todo вынести отдельно????
            $RLIST = $NSI->LoadUserRides(); // сегменты рейса
        } catch (Exception $e) {
            $app->log("Error during process load from NSI." . $e->getMessage(), 'FAILURE');
            die();
        }
        break;

    case 'load_user_rides_fast':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        try {
            $app->log("");
            $memoryLimitValue = !is_null($memoryLimit) ? $memoryLimit . 'M' : '2048M';
            if (ini_set('memory_limit', $memoryLimitValue) === false) {
                $app->log("Error: unable to increase the allowed memory to " . $memoryLimitValue, 'FAILURE');
                die();
            } else {
                $app->log("OK: increase allowed memory to " . $memoryLimitValue);
            }

            $allTdFlag = true;
            $_SERVER['REQUEST_METHOD'] = 'GET';
            $_SERVER['REQUEST_SCHEME'] = 'http';
            $NSI = new \lib\Avn\TerminalNSILoadLib();
            // todo вынести отдельно????
            $RLIST = $NSI->LoadUserRidesFast(); // сегменты рейса
            break;
        } catch (Exception $e) {
            $app->log("Error during process load from NSI." . $e->getMessage(), 'FAILURE');
            die();
        }

    case 'bill_emv_transaction':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $offset = null;
        if (isset($app->args->offset)) {
            $offset = (int)$app->args->offset;
        }
        $verbose = false;
        if (isset($app->args->vvv)) {
            $verbose = true;
        }

        $tarifFication_timeout = \lib\Cfg\Cfg::$api['tarification_timeout'] ?? 60;
        $tariffication_days_expired = \lib\Cfg\Cfg::$api['tarification_days_expired'] ?? 2;
        $tariffication_transfer_available_count = \lib\Cfg\Cfg::$api['tarification_transfer_available_count'] ?? null;

        $checkPassageRules = true;
        if (isset($app->args->no_check_passage_rules)) {
            $checkPassageRules = false;
        }
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_SCHEME'] = 'http';
        $EMV = new \lib\Avn\EMV\EMVTarifficator(
            isset($app->args->max_count) ? $app->args->max_count : null,
            $tarifFication_timeout,
            $tariffication_transfer_available_count,
            $tariffication_days_expired,
            $verbose,
            $checkPassageRules
        );
        //$EMV->RunTransactionTarifficate($offset);
        // Новый аргумент позволяющий, отключить сортировку в выборе транзакции для тарификации
        // При отключении сортировки значительно повышает скорость выборки
        $ordered = isset($app->args->ordered) ? (bool)$app->args->ordered : true;

        $EMV->RunTransactionTarifficate(
            $offset,
            $ordered
        );

        break;

    case 'check_emv_abonements':
        if (!isset($app->args->partner_id)) {
            $app->log('Не указан параметр partner_id', 'FAILURE');
            die();
        }
        if (!isset($vendor_config) || !$vendor_config) {
            $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);
        }

        if (!$vendor_config) {
            $app->log('partner_id не найден в config', 'FAILURE');
            die();
        }
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_SCHEME'] = 'http';
        $EMV = new \lib\Avn\EMV\EMVTarifficator(isset($app->args->max_count) ? $app->args->max_count : null);
        $EMV->RunAbonementCheck();
        break;

    case 'load_emv_stop_list':
        if (!isset($vendor_config) || !$vendor_config) {
            if (isset($app->args->partner_id)) {
                $vendor_config = $app->partner_model->get_vendor_api_config($app->args->partner_id);

                if (!$vendor_config) {
                    $app->log('partner_id не найден в config', 'FAILURE');
                }
            }
        }

        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_SCHEME'] = 'http';
        $NSI = new \lib\Avn\TerminalNSILoadLib();
        $NSI->LoadEMVStopList($app->args->partner_id ?? null);
        break;

    case 'check_transfers':
        $days = 2;
        if (isset($app->args->days)) {
            $days = (int)$app->args->days;
        }

        $EMV = new \lib\Avn\EMV\EMVTarifficator(isset($app->args->max_count) ? $app->args->max_count : null);
        $EMV->RunDisableOldTransfer($days);
        // RunDisableOldTransfer УДАЛЕНА - Функция отключающая пересадки для оптимизации и дальнейшего удаления.


        break;
    case 'other':
        if ($app->connector_manager->other_method($app->args) === false) {
            $app->log('Метод other в коннекторе партнера ' . $app->args->partner_id . ' не существует', 'FAILURE');
        }
        break;

    default:
        $app->log('Не верно задан action', 'FAILURE');
        break;
}
if (isset($app->args->api_timeout)) {
    Cfg::$apiTimeout = 90;
}
