<?php

namespace lib\Cfg;

error_reporting(-1);

/**
 * Глобальные настройки системы (значения по умолчанию).
 * Здесь параметры для рабочео сервера (прод), т.е. "значения по умолчанию для системы".<br>
 * Для локального/тестевого сервера нужно использовать Cfg/local.cfg.php для изменения default-параметров сервера под свои нужды.
 */
class Cfg
{
    private static $initialized = false;

    /**
     * Get environment variable with fallback to default value
     * @param string $key Environment variable name
     * @param mixed $default Default value if environment variable is not set
     * @return mixed
     */
    private static function getEnv(string $key, $default = null)
    {
        return $_ENV[$key] ?? getenv($key) ?? $default;
    }

    /**
     * Initialize configuration from environment variables
     */
    private static function initialize()
    {
        if (self::$initialized) {
            return;
        }

        // Initialize database configuration from environment variables
        self::$db = [
            'server' => self::getEnv('SDBP_DB_SERVER', 'localhost'),
            'user' => self::getEnv('SDBP_DB_USER', '***'),
            'psw' => self::getEnv('SDBP_DB_PSW', '***'),
            'dbname' => self::getEnv('SDBP_DB_NAME', '***'),
            'port' => self::getEnv('SDBP_DB_PORT', null),
            'socket' => self::getEnv('SDBP_DB_SOCKET', null),
            'debug' => self::getEnv('SDBP_DB_DEBUG', false),
        ];

        // Initialize time and date settings
        self::$defaultTZ = self::getEnv('SDBP_DEFAULT_TZ', 'Europe/Moscow');
        self::$dateFormat = self::getEnv('SDBP_DATE_FORMAT', 'Y-m-d');
        self::$datePickerFormat = self::getEnv('SDBP_DATE_PICKER_FORMAT', 'yyyy-mm-dd');

        // Initialize security settings
        self::$loginLenMin = (int)self::getEnv('SDBP_LOGIN_LEN_MIN', 3);
        self::$loginLenMax = (int)self::getEnv('SDBP_LOGIN_LEN_MAX', 255);
        self::$loginTriesMax = (int)self::getEnv('SDBP_LOGIN_TRIES_MAX', 3);
        self::$loginTriesLock = (int)self::getEnv('SDBP_LOGIN_TRIES_LOCK', 120);
        self::$pswLenMin = (int)self::getEnv('SDBP_PSW_LEN_MIN', 6);
        self::$pswLenMax = (int)self::getEnv('SDBP_PSW_LEN_MAX', 255);
        self::$rememberMeTime = (int)self::getEnv('SDBP_REMEMBER_ME_TIME', 2592000);
        self::$pswLenMinForCashRegisterCfg = (int)self::getEnv('SDBP_PSW_LEN_MIN_CASH_REGISTER', 8);
        self::$pswLenMaxForCashRegisterCfg = (int)self::getEnv('SDBP_PSW_LEN_MAX_CASH_REGISTER', 12);

        // Initialize API settings
        self::$GdsApiHost = self::getEnv('SDBP_GDS_API_HOST', 'api.sdbp.test');
        self::$apiTimeLimit = (int)self::getEnv('SDBP_API_TIME_LIMIT', 121);
        self::$apiConnectTimeout = (int)self::getEnv('SDBP_API_CONNECT_TIMEOUT', 30);
        self::$apiTimeout = (int)self::getEnv('SDBP_API_TIMEOUT', 90);
        self::$apiMobileDefaultPassangerPhone = self::getEnv('SDBP_API_MOBILE_DEFAULT_PHONE', '+79999999999');
        self::$apiMobileDefaultPassangerEmail = self::getEnv(
            'SDBP_API_MOBILE_DEFAULT_EMAIL',
            '<EMAIL>'
        );
        self::$maxRows = (int)self::getEnv('SDBP_MAX_ROWS', 1048576);

        // Initialize cache settings
        self::$cache = [
            'className' => self::getEnv('SDBP_CACHE_CLASS', 'File'),
            'duration' => self::getEnv('SDBP_CACHE_DURATION', '+1 hours'),
            'prefix' => self::getEnv('SDBP_CACHE_PREFIX', 'sdbp_default'),
            'path' => self::getEnv('SDBP_CACHE_PATH', '../cache/'),
            'mask' => self::getEnv('SDBP_CACHE_MASK', 0777),
        ];



        // Initialize admin module settings
        self::$admin = [
            'cache' => [
                'className' => self::getEnv('SDBP_CACHE_CLASS', 'File'),
                'duration' => self::getEnv('SDBP_CACHE_DURATION', '+1 hours'),
                'prefix' => self::getEnv('SDBP_CACHE_PREFIX', 'sdbp_admin'),
                'path' => self::getEnv('SDBP_CACHE_PATH', '../cache/'),
                'mask' => self::getEnv('SDBP_CACHE_MASK', 0777),
            ],

            'payment_owner_system_title' => self::getEnv('SDBP_ADMIN_PAYMENT_OWNER_SYSTEM_TITLE', ''),
            'gds_api' => [
                'url' => self::getEnv('SDBP_ADMIN_GDS_API_URL', 'http://api.sdbp.local'),
            ],
            'keycloak' => [
                'client_id' => self::getEnv('SDBP_ADMIN_KEYCLOAK_CLIENT_ID', null),
                'client_secret' => self::getEnv('SDBP_ADMIN_KEYCLOAK_CLIENT_SECRET', null),
                'realm' => self::getEnv('SDBP_ADMIN_KEYCLOAK_REALM', null),
                'base_url' => self::getEnv('SDBP_ADMIN_KEYCLOAK_BASE_URL', 'http://keycloak:8080'),
                'is_enabled' => (bool)self::getEnv('SDBP_ADMIN_KEYCLOAK_ENABLED', false),
            ],
            'telegram_fail_send' => (bool)self::getEnv('SDBP_ADMIN_TELEGRAM_FAIL_SEND', false),
            'telegram_token' => self::getEnv('SDBP_ADMIN_TELEGRAM_TOKEN', ''),
            'telegram_fail_chat_id' => self::getEnv('SDBP_ADMIN_TELEGRAM_FAIL_CHAT_ID', ''),
            'api_mobile' => self::getEnv('SDBP_ADMIN_API_MOBILE', 'http://am.sdbp.local'),
            'lkp' => [
                'endpoint' => self::getEnv('SDBP_ADMIN_LKP_ENDPOINT', 'localhost:5000'),
                'enabled' => (bool)self::getEnv('SDBP_ADMIN_LKP_ENABLED', false),
                'log_level' => (int)self::getEnv('SDBP_ADMIN_LKP_LOG_LEVEL', 2),
                'ssl' => (bool)self::getEnv('SDBP_ADMIN_LKP_SSL', true),
                'update_status' => (int)self::getEnv('SDBP_ADMIN_LKP_UPDATE_STATUS', 60000),
            ],
            'region_id' => self::getEnv('SDBP_ADMIN_REGION_ID', '0'),
            'map_provider' => self::getEnv('SDBP_ADMIN_MAP_PROVIDER', 'osm'),
            'language' => self::getEnv('SDBP_ADMIN_LANGUAGE', 'ru'),
        ];

        // Initialize API module settings
        self::$api['outer_api_log'] = (bool)self::getEnv('SDBP_API_OUTER_API_LOG', true);

        // Initialize logging settings
        self::$apilog = [
            'success' => [
                'log' => (bool)self::getEnv('SDBP_LOG_SUCCESS', false),
                'method' => explode(',', self::getEnv('SDBP_LOG_METHODS', 'POST,GET')),
                'log_answer' => (bool)self::getEnv('SDBP_LOG_ANSWER', false),
                'log_server' => (bool)self::getEnv('SDBP_LOG_SERVER', false),
                'log_get' => (bool)self::getEnv('SDBP_LOG_GET', false),
                'log_post' => (bool)self::getEnv('SDBP_LOG_POST', false),
                'log_files' => (bool)self::getEnv('SDBP_LOG_FILES', false),
                'log_sql' => (bool)self::getEnv('SDBP_LOG_SQL', false),
            ],
            'error' => [
                'log' => (bool)self::getEnv('SDBP_LOG_ERROR', true),
                'method' => explode(',', self::getEnv('SDBP_LOG_METHODS', 'POST,GET,PUT,DELETE')),
                'log_answer' => (bool)self::getEnv('SDBP_LOG_ANSWER', true),
                'log_server' => (bool)self::getEnv('SDBP_LOG_SERVER', true),
                'log_get' => (bool)self::getEnv('SDBP_LOG_GET', true),
                'log_post' => (bool)self::getEnv('SDBP_LOG_POST', true),
                'log_files' => (bool)self::getEnv('SDBP_LOG_FILES', true),
                'log_sql' => (bool)self::getEnv('SDBP_LOG_SQL', true),
            ],
        ];

        // Initialize missing properties
        self::$dirForCashRegisterCfg = self::getEnv('SDBP_DIR_CASH_REGISTER_CFG', '../config/cash_register/');
        self::$exludeCitiesOnNameFromList = self::getEnv('SDBP_EXCLUDE_CITIES_ON_NAME_FROM_LIST', []);
        self::$showAllCitiesOnYMap = (bool)self::getEnv('SDBP_SHOW_ALL_CITIES_ON_YMAP', false);

        self::$initialized = true;
    }

    /**
     * Параметры доступа к БД.<br>
     * <b>server</b> : Наименование SQL-сервера.<br>
     * <b>user</b> : Имя юзера SQL-сервера.<br>
     * <b>psw</b> : Пароль доступа.<br>
     * <b>name</b> : Наименование базы данных на SQL-сервере.<br>
     * @var array
     */
    public static $db = [
        'server' => null,
        'user' => null,
        'psw' => null,
        'dbname' => null,
        'port' => null,
        'socket' => null,
        'debug' => false,
    ];

    /** SQL-формат времени. */
    public const SqlDatetimeFormat = 'Y-m-d H:i:s';
    /** SQL-формат даты. */
    public const SqlDateFormat = 'Y-m-d';
    /** Ограничение на число возвращаемых строк SQL-запросом (по умолчанию). */
    public const SqlLimitDefault = 1048576; // 2^20
    /** Основной файл локализации текстовых констант. */
    public const TR = 't';
    /** Временная зона по умолчанию. @var string */
    public static $defaultTZ = 'Europe/Moscow';

    // -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    /** Полный путь от корня к файлу сертификатов.<br>В основном используется для работы с https. */
    public const Cacert = '/Resources/cacert.pem'; // Новый файл сертификатов можно взять там https://curl.haxx.se/docs/caextract.html
    // Min/Max -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    /** Минимальная длина Логина. */
    public static $loginLenMin = 3;
    /** Максимальная длина Логина. */
    public static $loginLenMax = 255;
    /** Максимальное число попыток залогинеться (до блокировки). */
    public static $loginTriesMax = 3;
    /** Время блокирования входа при достижении макс.числа попыток залогинется. */
    public static $loginTriesLock = 120;
    /** Минимальная длина пароля. */
    public static $pswLenMin = 6;
    /** Максимальная длина пароля. */
    public static $pswLenMax = 255;
    /** Время жизни для "запомнить меня"/автовход (сек).  @var int */
    public static $rememberMeTime = 2592000; // 30 дней
    /** Минимальная длина пароля для конфигурации кассы. */
    public static $pswLenMinForCashRegisterCfg = 8;
    /** Максимальная длина пароля для конфигурации кассы. */
    public static $pswLenMaxForCashRegisterCfg = 12;
    //
    /** Каталог для временных файлов относительно корневого каталога. */
    public const DirTmp = 'tmp/';
    /** Каталог для конфигурационных файлов касс.<br>Устанавливается при первом обращении к Cfg.  @var string */
    public static $dirForCashRegisterCfg; // настройку см. внизу
    /** Каталог шаблонов относительно корневого каталога. */
    public const DirTemplates = '/Views';

    //
    /** Базовый формат даты. @var string */
    public static $dateFormat = 'Y-m-d';
    /** Формат для DatePicker (эквивалент {@link Cfg::$dateFormat}) @var string */
    public static $datePickerFormat = 'yyyy-mm-dd';
    /** Код языка интерфейса по умолчанию. @var string */
    public const LangUI = '';
    // Некоторые значения "по умолчанию" ---------------------------------------------------------------------------------------------------------------------------------------------------------------
    /** см. {@link DataSite::$sunIsLast} */
    public const SunIsLast = true;
    /** Валюта по умолчанию. */
    public const baseCurrency = 'rub';
    // Параметры ГДС
    /** Хост ГДС. Если не указан, то будет(должен быть) домен третьего уровня "api". @var string */
    public static $GdsApiHost = 'api.sdbp.test'; // типа 'api.xz-gds.zzz';
    /**
     * Максимальное время работы скрипта при запросе к API.<br>
     * Значение параметра должно быть больше, чем значение {@link Cfg::$ApiTimeout $ApiTimeout}, иначе работа скрипта может быть прервана раньше, чем будет получен ответ от API.
     * <br>Например, используется при запросе ГДС к API ЛГДС.
     * @var int
     */
    public static $apiTimeLimit = 121;
    /** Максимальное время ожидания соединения с сервером при запросе к API (параметр cURL).<br>Например, используется при запросе ГДС к API ЛГДС. @var int */
    public static $apiConnectTimeout = 30;
    /**
     * Максимальное время ожидания окончания ответа от сервера при запросе к API (параметр cURL).<br>
     * Значение параметра должно быть больше, чем значение {@link Cfg::$ApiConnectTimeout $ApiConnectTimeout} плюс время получения ответа,
     * иначе работа cURL может быть прервана раньше, чем будет получен ответ от API.
     * <br>Например, используется при запросе ГДС к API ЛГДС.
     * @var int
     */
    public static $apiTimeout = 90;
    // телефон подставляемый по умолчанию при бронировании через МК если не указан отдельно
    /** Описание см. {@link DataSite::$apiMobileDefaultPassangerPhone} */
    public static $apiMobileDefaultPassangerPhone = '+79999999999';
    /** Описание см. {@link DataSite::$apiMobileDefaultPassangerEmail} */
    public static $apiMobileDefaultPassangerEmail = '<EMAIL>';
    /** Описание см. {@link DataSite::$apiMaxRows} */
    public static $maxRows = 1048576; // 2^20
    /** Описание см. {@link DataSite::$exludeCitiesOnNameFromList} */
    public static $exludeCitiesOnNameFromList;
    public static $showAllCitiesOnYMap = false;

    // ========================================================================================================
    // НОВАЯ СИСТЕМА КОНФИГУРАЦИИ - ПАРАМЕТРЫ ПО МОДУЛЯМ
    // ========================================================================================================

    /** Общие параметры кэширования. @var array */
    public static $cache = [
        'className' => 'File',
        'duration' => '+1 hours',
        'prefix' => 'sdbp_default',
        'path' => '../cache/',
        'mask' => 0777,
    ];



    /** Общий параметр логирования vendor API. @var bool */
    public static $vendorapiTmpbookLog = false;

    /** Общие параметры логирования API. @var array */
    public static $apilog = [
        'success' => [
            'log' => false,
            'method' => ['POST', 'GET'],
            'log_answer' => false,
            'log_server' => false,
            'log_get' => false,
            'log_post' => false,
            'log_files' => false,
            'log_sql' => false,
        ],
        'error' => [
            'log' => true,
            'method' => ['POST', 'GET', 'PUT', 'DELETE'],
            'log_answer' => true,
            'log_server' => true,
            'log_get' => true,
            'log_post' => true,
            'log_files' => true,
            'log_sql' => true,
        ],
    ];

    // ========================================================================================================
    // ПАРАМЕТРЫ ADMIN МОДУЛЯ
    // ========================================================================================================

    /** Параметры admin модуля. @var array */
    public static $admin = [
        'cache' => [
            'className' => 'File',
            'duration' => '+1 hours',
            'prefix' => 'sdbp_admin',
            'path' => '../cache/',
            'mask' => 0777,
        ],


        'payment_owner_system_title' => '',
        'gds_api' => [
            'url' => 'http://api.sdbp.local',
        ],
        'keycloak' => [
            'client_id' => null,
            'client_secret' => null,
            'realm' => null,
            'base_url' => 'http://keycloak:8080',
            'is_enabled' => false,
        ],
        'telegram_fail_send' => false,
        'telegram_token' => '76xxxxxx:xxx',
        'telegram_fail_chat_id' => '-1001xxx',
        'api_mobile' => 'http://am.sdbp.local',

        'lkp' => [
            'endpoint' => 'localhost:5000',
            'enabled' => false,
            'log_level' => 2,
            'ssl' => true,
            'update_status' => 60000,
        ],
        'region_id' => '0',
        'force_custom_pan_hash_algorithm' => null,
        'expiration_date_shift_template' => 0,
        'max_count_emv_stoplist_abonements' => 1,
        'map_provider' => 'osm',
        'language' => 'ru',
    ];

    // ========================================================================================================
    // ПАРАМЕТРЫ API МОДУЛЯ
    // ========================================================================================================

    /** Параметры api модуля. @var array */
    public static $api = [
        'cache' => [
            'connection' => 'redis',
            'connections' => [
                'default' => [
                    'className' => \Cake\Cache\Engine\FileEngine::class,
                    'duration' => '+1 hours',
                    'prefix' => 'sdbp_api',
                    'path' => '../cache/',
                    'mask' => 0777,
                ],
                'redis' => [
                    'className' => \Cake\Cache\Engine\RedisEngine::class,
                    'host' => 'redis',
                    'port' => 6379,
                    'timeout' => 0,
                    'persistent' => false,
                    'password' => '',
                    'database' => 0,
                    'duration' => '+1 hours',
                    'prefix' => 'sdbp-kube_api_',
                ],
            ],
        ],

        'outer_api_log' => true,
        'outer_api_log_sql_chunk_size' => 1,
        'parner_xml' => [],
        'buy_ticket_cnt_max' => 5,

        'telegram_fail_send' => true,
        'telegram_token' => "**********************************************",
        'telegram_fail_chat_id' => "-1001743283039",
        'auto_refund' => 0,
        'default_agent_id' => 1,
        'nsi_http_timeout' => 30,
        'goldCrown' => [
            'host' => 'localhost',
            'port' => 21,
            'username' => 'foo',
            'password' => 'pass',
            'rootFolder' => '/',
            'sourceFolder' => '/Korona',
            'answerFolder' => '/Troika',
            'outputFolder' => '/Troika',
        ],
        'tarification_timeout' => 0,
        'tarification_days_expired' => 2,
        'tarification_transfer_available_count' => null,
        'expiration_date_shift_template' => 0,
        'transfer_leftovers' => false,
        'autoSetUserIDInAPI' => false,
        'checkTimezoneOff' => false,
        'max_tt_request_count' => 1000,
        'keycloak' => [
            'client_id' => 'some_client_id',
            'client_secret' => 'secret',
            'realm' => 'some_realm',
            'base_url' => 'http://keycloak:8080',
            'is_enabled' => true,
        ],
    ];

    // ========================================================================================================
    // ПАРАМЕТРЫ API-MOBILE МОДУЛЯ
    // ========================================================================================================

    /** Параметры api-mobile модуля. @var array */
    public static $apiMobile = [
        'cache' => [
            'className' => 'File',
            'duration' => '+1 hours',
            'prefix' => 'sdbp_api_mobile',
            'path' => '../cache/',
            'mask' => 0777,
        ],

        'outer_api_log' => true,
        'outer_api_log_sql_chunk_size' => 1,
        'parner_xml' => [],
        'buy_ticket_cnt_max' => 5,
        'max_terminal_log_count' => 5,
        'api_mobile' => [
            'url' => 'http://api-mobile.sdbp.local',
        ],


        'telegram_fail_send' => true,
        'telegram_token' => "**********************************************",
        'telegram_fail_chat_id' => "-1001743283039",

        'autoSetUserIDInAPI' => false,
        'checkTimezoneOff' => false,
    ];

    // ========================================================================================================
    // ПАРАМЕТРЫ GEO МОДУЛЯ
    // ========================================================================================================

    /** Параметры geo модуля. @var array */
    public static $geo = [

    ];

    // ========================================================================================================
    // ПАРАМЕТРЫ FINSTAT МОДУЛЯ
    // ========================================================================================================

    /** Параметры finstat модуля. @var array */
    public static $finstat = [
        'payment_owner_system_title' => '',
    ];

    /** Файл локальных настроек. @var string */
    public const localCfgFile = '/local.cfg.php';

    // ========================================================================================================
    // МЕТОДЫ ДЛЯ НОВОЙ СИСТЕМЫ КОНФИГУРАЦИИ
    // ========================================================================================================

    /**
     * Получить конфигурацию для указанного модуля в формате, совместимом с lbaf\config\config
     * @param string|null $module Название модуля (admin, api, geo, finstat, api-mobile). Если null - автоопределение
     * @return \stdClass Объект конфигурации
     */
    public static function getConfig($module = null)
    {
        self::initialize();
        if (!$module) {
            $module = self::detectModule();
        }

        $config = new \stdClass();

        // Базовые параметры БД
        $config->database = (object)[
            'mysql' => (object)[
                'host' => self::$db['server'],
                'user' => self::$db['user'],
                'password' => self::$db['psw'],
                'database' => self::$db['dbname'],
                'port' => self::$db['port'],
                'socket' => self::$db['socket'],
            ],
            'debug' => self::$db['debug'] ?? false,
        ];

        // Общие параметры
        $config->vendorapi_tmpbook_log = self::$vendorapiTmpbookLog;

        $config->apilog = (object)[
            'success' => (object)self::$apilog['success'],
            'error' => (object)self::$apilog['error'],
        ];

        // Instance prefix для cron control
        $config->instance_prefix = $_ENV['INSTANCE_PREFIX'] ?? '000:noname:0';

        // Модуль-специфичные параметры
        switch ($module) {
            case 'admin':
                self::applyAdminConfig($config);
                break;
            case 'api':
                self::applyApiConfig($config);
                break;
            case 'api-mobile':
                self::applyApiMobileConfig($config);
                break;
            case 'geo':
                self::applyGeoConfig($config);
                break;
            case 'finstat':
                self::applyFinstatConfig($config);
                break;
            // Все модули добавлены
            default:
                // Базовая конфигурация
                $config->cache = self::$cache;
                break;
        }

        return $config;
    }

    /**
     * Применить конфигурацию admin модуля
     * @param \stdClass $config
     */
    private static function applyAdminConfig(\stdClass $config)
    {
        // Параметры, которые должны остаться массивами для совместимости
        $arrayParams = ['cache', 'keycloak', 'type_vendor', 'type_reseller'];

        foreach (self::$admin as $key => $value) {
            if (in_array($key, $arrayParams) || (is_array($value) && isset($value[0]))) {
                // Оставляем как массив для совместимости
                $config->$key = $value;
            } elseif (is_array($value)) {
                // Ассоциативный массив - конвертируем в объект
                $config->$key = (object)$value;
            } else {
                // Скалярное значение
                $config->$key = $value;
            }
        }

        // Специальные объекты для admin - gds_api уже обработан выше как объект

        // Добавляем константы типов vendor/reseller
        // Эти массивы будут заполнены реальными константами при первом обращении к ним
        $config->type_vendor_list = [];
        $config->type_reseller_list = [];

        // Заполняем массивы, если классы доступны
        if (class_exists('\model\partner')) {
            $config->type_vendor_list = [
                \model\partner::TYPE_VENDOR_NONE => 'Не вендор',
                \model\partner::TYPE_VENDOR_CRM => 'CRM',
                \model\partner::TYPE_VENDOR_CRM_AWPC => 'CRM+АРМК',
                \model\partner::TYPE_VENDOR_API => 'API',
            ];

            $config->type_reseller_list = [
                \model\partner::TYPE_RESELLER_NONE => 'Не реселлер',
                \model\partner::TYPE_RESELLER_API => 'API',
                \model\partner::TYPE_RESELLER_API_AWPC => 'API+АРМКРеселлер',
            ];
        }

        // Заполняем группы, если классы доступны (type_vendor и type_reseller уже установлены как массивы выше)
        if (class_exists('\model\partner') && class_exists('\model\group')) {
            $config->type_vendor = [
                \model\partner::TYPE_VENDOR_NONE => [],
                \model\partner::TYPE_VENDOR_CRM => [
                    \model\group::GROUP_VENDOR_AGENT,
                ],
                \model\partner::TYPE_VENDOR_CRM_AWPC => [
                    \model\group::GROUP_VENDOR_ADMIN,
                    \model\group::GROUP_VENDOR_CASHIER,
                    \model\group::GROUP_VENDOR_DISPATCHER,
                    \model\group::GROUP_VENDOR_CASHIER_SENIOR,
                ],
                \model\partner::TYPE_VENDOR_API => [
                    \model\group::GROUP_VENDOR_API_STATISTIC,
                ],
            ];

            $config->type_reseller = [
                \model\partner::TYPE_RESELLER_NONE => [],
                \model\partner::TYPE_RESELLER_API => [
                    \model\group::GROUP_RESELLER_ADMIN,
                    \model\group::GROUP_RESELLER_TICKET_REFUND,
                    \model\group::GROUP_RESELLER_REFERENCE,
                ],
                \model\partner::TYPE_RESELLER_API_AWPC => [
                    \model\group::GROUP_RESELLER_ADMIN,
                    \model\group::GROUP_RESELLER_CASHIER,
                    \model\group::GROUP_RESELLER_TICKET_REFUND,
                    \model\group::GROUP_RESELLER_REFERENCE,
                ],
            ];
        }
    }

    /**
     * Применить конфигурацию api модуля
     * @param \stdClass $config
     */
    private static function applyApiConfig(\stdClass $config)
    {
        // Параметры, которые должны остаться массивами для совместимости
        $arrayParams = ['cache', 'parner_xml', 'goldCrown', 'keycloak'];

        foreach (self::$api as $key => $value) {
            if (in_array($key, $arrayParams) || (is_array($value) && isset($value[0]))) {
                // Оставляем как массив для совместимости
                $config->$key = $value;
            } elseif (is_array($value)) {
                // Ассоциативный массив - конвертируем в объект
                $config->$key = (object)$value;
            } else {
                // Скалярное значение
                $config->$key = $value;
            }
        }
    }

    /**
     * Применить конфигурацию api-mobile модуля
     * @param \stdClass $config
     */
    private static function applyApiMobileConfig(\stdClass $config)
    {
        // Параметры, которые должны остаться массивами для совместимости
        $arrayParams = ['cache', 'parner_xml', 'api_mobile'];

        foreach (self::$apiMobile as $key => $value) {
            if (in_array($key, $arrayParams) || (is_array($value) && isset($value[0]))) {
                // Оставляем как массив для совместимости
                $config->$key = $value;
            } elseif (is_array($value)) {
                // Ассоциативный массив - конвертируем в объект
                $config->$key = (object)$value;
            } else {
                // Скалярное значение
                $config->$key = $value;
            }
        }
    }

    /**
     * Применить конфигурацию geo модуля
     * @param \stdClass $config
     */
    private static function applyGeoConfig(\stdClass $config)
    {
        // Geo модуль имеет минимальную конфигурацию
        foreach (self::$geo as $key => $value) {
            if (is_array($value)) {
                $config->$key = (object)$value;
            } else {
                $config->$key = $value;
            }
        }
    }

    /**
     * Применить конфигурацию finstat модуля
     * @param \stdClass $config
     */
    private static function applyFinstatConfig(\stdClass $config)
    {
        // Finstat модуль имеет минимальную конфигурацию
        foreach (self::$finstat as $key => $value) {
            if (is_array($value)) {
                $config->$key = (object)$value;
            } else {
                $config->$key = $value;
            }
        }
    }

    /**
     * Определить текущий модуль по пути выполнения
     * @return string
     */
    private static function detectModule()
    {
        $scriptPath = $_SERVER['SCRIPT_FILENAME'] ?? '';
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';

        // Определяем по пути к скрипту
        if (strpos($scriptPath, '/admin/') !== false) {
            return 'admin';
        }
        if (strpos($scriptPath, '/api-mobile/') !== false) {
            return 'api-mobile';
        }
        if (strpos($scriptPath, '/api/') !== false) {
            return 'api';
        }
        if (strpos($scriptPath, '/geo/') !== false) {
            return 'geo';
        }
        if (strpos($scriptPath, '/finstat/') !== false) {
            return 'finstat';
        }

        // Определяем по URI
        if (strpos($requestUri, '/admin/') === 0) {
            return 'admin';
        }
        if (strpos($requestUri, '/api-mobile/') === 0) {
            return 'api-mobile';
        }
        if (strpos($requestUri, '/api/') === 0) {
            return 'api';
        }
        if (strpos($requestUri, '/geo/') === 0) {
            return 'geo';
        }
        if (strpos($requestUri, '/finstat/') === 0) {
            return 'finstat';
        }

        // По умолчанию
        return 'admin';
    }
}

// Инициализация конфигурации/настроек при первом обращении к классу... ----------------------------------------------------------------------------------------------------------------------------
//define('MYSQLI_Object',99,true);
define('MYSQLI_Object', 99);

if (!isset($_SESSION)) {
    $_SESSION = [];
} // линуксоиды не создают этот массив изначально :(

if (file_exists(__DIR__.Cfg::localCfgFile)) {
    require_once __DIR__.Cfg::localCfgFile;
}
