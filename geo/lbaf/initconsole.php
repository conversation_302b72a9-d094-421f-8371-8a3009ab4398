<?php

use lib\Site;

chdir($_SERVER['DOCUMENT_ROOT']);

//загружаем основной инит
require('lbaf/init/initcore.php');

//лайфхак. подменяем app что бы не сработал автолоадер
require_once('core/appconsole.php');

//делаем первичную инициализацию синглтона для консольных аргументов
$app = \core\app::getInstance(isset($argv) ? $argv : []);
    // дань старым наваротам, чтобы был доступен нужный конфиг и прочее...
    Site::$User->Run(isset($app->user) ? ($app->user->user_id ?? null) : null);


$strCmd = (new Cmd2String())->parse($_SERVER);
if ($cronControl->issetByRaw($strCmd, $pidProcess)) {
    $app->log("CronControl: The process already exists. Stopping the script", 'WARNING');
    exit();
}
/* конец Cron Control */

register_shutdown_function(function () {
    global $cronControl;
    $cronControl->end(); // Cron Control
});
